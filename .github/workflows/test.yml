name: Tests

on:
  push:
    branches:
      - main
      - dev
  pull_request:
    branches:
      - main

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version: [ '3.9', '3.11', '3.12' ]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pytest
          pip install -r requirements_dev.txt
      - name: Lint with flake8
        run: |
          ruff check

      - name: Run tests
        run: |
          pytest -v tests/
