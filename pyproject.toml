[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tg-signer"
dynamic = ["version"]
description = "Telegram signer"
authors = [
    { name = "Amchii", email = "<EMAIL>" }
]
license = "BSD-3-Clause"
license-files = ["LICENSE*"]
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Intended Audience :: Developers",
    "License :: OSI Approved :: BSD License",
    "Programming Language :: Python :: 3 :: Only"
]
dependencies = [
    "kurigram",
    "click",
    "pydantic",
    "openai",
    "croniter",
    "json_repair",
    "typing-extensions",
    "httpx"
]

[project.urls]
Homepage = "https://github.com/amchii/tg-signer"
Repository = "https://github.com/amchii/tg-signer"

[project.optional-dependencies]
tgcrypto = [
    "tgcrypto"
]
speedup = [
    "tgcrypto"
]

[project.scripts]
tg-signer = "tg_signer.__main__:signer"

[tool.setuptools]
include-package-data = true

[tool.setuptools.dynamic]
version = { attr = "tg_signer.__version__" }

[tool.setuptools.packages.find]
exclude = ["tests*"]

[tool.ruff]
line-length = 88

[tool.ruff.lint]
extend-select = [
    "I", # isort
    "B", # flake8-bugbear
    "W", # pycodestyle
    "C4", # flake8-comprehensions
]
ignore = [
    "E501", # line too long, handled by black
    "B008", # do not perform function calls in argument defaults
    "C901", # too complex
    "W191", # indentation contains tabs
    "B019", # Use of `functools.lru_cache` or `functools.cache` on methods can lead to memory leaks
    "B904"  # raise without from inside except
]

[tool.pytest.ini_options]
asyncio_default_fixture_loop_scope = "function"
