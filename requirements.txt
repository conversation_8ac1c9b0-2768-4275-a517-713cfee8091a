# This file was autogenerated by uv via the following command:
#    uv pip compile --refresh pyproject.toml -o requirements.txt
annotated-types==0.7.0
    # via pydantic
anyio==4.8.0
    # via
    #   httpx
    #   openai
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
click==8.1.8
    # via tg-signer (pyproject.toml)
croniter==6.0.0
    # via tg-signer (pyproject.toml)
distro==1.9.0
    # via openai
h11==0.14.0
    # via httpcore
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via
    #   tg-signer (pyproject.toml)
    #   openai
idna==3.10
    # via
    #   anyio
    #   httpx
jiter==0.8.2
    # via openai
json-repair==0.39.1
    # via tg-signer (pyproject.toml)
kurigram==2.2.4
    # via tg-signer (pyproject.toml)
openai==1.78.1
    # via tg-signer (pyproject.toml)
pyaes==1.6.1
    # via kurigram
pydantic==2.10.6
    # via
    #   tg-signer (pyproject.toml)
    #   openai
pydantic-core==2.27.2
    # via pydantic
pysocks==1.7.1
    # via kurigram
python-dateutil==2.9.0.post0
    # via croniter
pytz==2025.1
    # via croniter
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   openai
tqdm==4.67.1
    # via openai
typing-extensions==4.12.2
    # via
    #   tg-signer (pyproject.toml)
    #   anyio
    #   openai
    #   pydantic
    #   pydantic-core
